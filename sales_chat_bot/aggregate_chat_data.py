import csv
from datetime import datetime
from pymongo import MongoClient
from typing import List, Dict, Any

def aggregate_chat_data_to_csv(
    connection_string: str,
    database_name: str,
    collection_name: str = "responses",
    output_file: str = "chat_data.csv"
):
    """
    Aggregates chat history and latest messages from MongoDB collection to CSV.
    
    Args:
        connection_string: MongoDB connection string
        database_name: Name of the database
        collection_name: Name of the collection (default: "responses")
        output_file: Output CSV file path
    """
    
    # Connect to MongoDB
    client = MongoClient(connection_string)
    db = client[database_name]
    collection = db[collection_name]
    
    # Prepare CSV data
    csv_data = []
    
    # Process each document
    for doc in collection.find():
        user_name = doc.get("user_name", "")
        conversation_id = doc.get("conversation_id", "")
        timestamp = doc.get("timestamp", "")
        
        # Get latest message
        latest_message = doc.get("request", {}).get("message", "")
        
        # Process chat history
        chat_history = doc.get("request", {}).get("chat_history", [])
        
        for chat_item in chat_history:
            csv_data.append({
                "user_name": user_name,
                "conversation_id": conversation_id,
                "timestamp": timestamp,
                "message_type": "chat_history",
                "role": chat_item.get("role", ""),
                "content": chat_item.get("content", "")
            })
        
        # Add latest message
        if latest_message:
            csv_data.append({
                "user_name": user_name,
                "conversation_id": conversation_id,
                "timestamp": timestamp,
                "message_type": "latest_message",
                "role": "user",
                "content": latest_message
            })
    
    # Write to CSV
    if csv_data:
        fieldnames = ["user_name", "conversation_id", "timestamp", "message_type", "role", "content"]
        
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(csv_data)
        
        print(f"Successfully exported {len(csv_data)} records to {output_file}")
    else:
        print("No data found to export")
    
    client.close()

# Usage example
if __name__ == "__main__":
    aggregate_chat_data_to_csv(
        connection_string="mongodb://localhost:27017/",
        database_name="your_database_name",
        output_file="chat_aggregated_data.csv"
    )